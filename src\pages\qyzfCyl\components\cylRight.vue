<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-18 19:49:00
-->
<body>
  <div id="app" v-cloak>
    <div class="container">
      <div class="con">
        <div class="con-item">
          <div class="two-title">
            <div class="title-text">
              <div>产业主体情况</div>
            </div>
          </div>
          <div class="item-content cyzyClass">
            <div class="upClass">
              <div class="txtS">规上企业总数</div>
              <div class="countCard">
                <div class="number" v-for="(item, i) in '2801'" :key="i">
                  <div class="numbg" v-if="item!=','&&item!='.'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div v-else>{{item}}</div>
                </div>
              </div>
              <div class="txtS">家</div>
            </div>

            <div class="midClass">
              <div class="cardS" @click="changeQy(0)">
                <div class="cardBg" v-show="qyIndex != 0"></div>
                <div class="cardBgTwo" v-show="qyIndex == 0"></div>
                <div class="txtTwo">链主培育企业</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in String(zsArr[1].zs)" :key="i">
                    <div class="numbgTwo" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numTwo" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:38px;">家</span>
                </div>
              </div>
              <div class="cardS" @click="changeQy(1)">
                <div class="cardBg" v-show="qyIndex != 1"></div>
                <div class="cardBgTwo" v-show="qyIndex == 1"></div>
                <div class="txtTwo">骨干企业</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in String(zsArr[2].zs)" :key="i">
                    <div class="numbgTwo" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numTwo" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:38px;">家</span>
                </div>
              </div>
              <div class="cardS" @click="changeQy(2)">
                <div class="cardBg" v-show="qyIndex != 2"></div>
                <div class="cardBgTwo" v-show="qyIndex == 2"></div>
                <div class="txtTwo">星火企业</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in String(zsArr[0].zs)" :key="i">
                    <div class="numbgTwo" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numTwo" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:38px;">家</span>
                </div>
              </div>
            </div>

            <div class="charOneClass" id="chartOne"></div>
          </div>
        </div>

        <div class="con-item">
          <div class="two-title">
            <div class="title-text">
              <div>产业资源</div>
            </div>
          </div>
          <div class="item-content cyzyClass">
            <div class="partOne">
              <div class="subTitle">产业政策</div>
              <div class="partBot">
                <div class="innerBox boderOne">
                  <div class="ltxt">政策数量</div>
                  <div class="numberTwo">
                    <div v-for="(item, i) in cyzc.zcsl" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">条</span>

                  </div>
                </div>
                <div class="innerBox boderOne">
                  <div class="ltxt">兑付企业数</div>
                  <div class="numberTwo">
                    <div v-for="(item, i) in cyzc.dfqys" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">家</span>
                  </div>
                </div>
                <div class="innerBox">
                  <div class="ltxt">政策兑付金额</div>
                  <div class="numberTwo">
                    <div v-for="(item, i) in cyzc.zcdfje" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">亿</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="partOne">
              <div class="subTitle">人才资源</div>
              <div class="partBot">
                <div class="innerBox boderOne">
                  <div class="ltxt">乡贤人才</div>
                  <div class="numberTwo">
                    <div v-for="(item, i) in rczy.xxrc" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">人</span>
                  </div>
                </div>
                <div class="innerBox boderOne">
                  <div class="ltxt">婺商企业家</div>
                  <div class="numberTwo">
                    <!-- <div v-for="(item, i) in rczy.wsqyj" :key="i"> -->
                    <div v-for="(item, i) in '1345'" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">人</span>
                  </div>
                </div>
                <div class="innerBox">
                  <div class="ltxt">高素质劳动者</div>
                  <div class="numberTwo">
                    <div v-for="(item, i) in rczy.gszldz" :key="i">
                      <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                      </div>
                      <div class="numtxt" v-else>{{item}}</div>
                    </div>
                    <span class="numtxt" style="font-size:32px;margin-top:64px;">万</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="con">
        <div class="con-item">
          <div class="two-title">
            <div class="title-text">
              <div>产业规模</div>
            </div>
          </div>
          <div class="item-content cyzyClass">
            <div class="upTwoClass">
              <div class="upsClass">
                <div class="txtS">规上总产值</div>
                <div class="countCard">
                  <div class="number" v-for="(item, i) in gszcz.value" :key="i">
                    <div class="numbg" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div v-else>{{item}}</div>
                  </div>
                </div>
                <div class="txtS">亿元</div>
              </div>
              <div class="downClass">
                <div class="twoClass">
                  <div class="tbLabel">增速</div>
                  <div class="txtNum">{{gszcz.zs}}%</div>
                </div>
              </div>
            </div>
            <div class="chartTwoClass" id="chartTwo"></div>
          </div>
        </div>

        <div class="con-item newbottom">
          <div class="partOne">
            <div class="subTitle">技术攻关（揭榜挂帅）</div>
            <div class="partBot">
              <div class="innerBox boderOne">
                <div class="ltxt">揭榜数量</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in jsgg.jbsl" :key="i">
                    <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:64px;">个</span>
                </div>
              </div>
              <div class="innerBox boderOne">
                <div class="ltxt">揭榜总金额</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in jsgg.jbzje" :key="i">
                    <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:64px;">亿</span>
                </div>
              </div>
              <div class="innerBox">
                <div class="ltxt">揭榜率</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in jsgg.jbl" :key="i">
                    <div class="numtxt" v-if="item!=','&&item!='.'&&item!='-'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:64px;">%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="partOne">
            <div class="subTitle">科创平台</div>
            <div class="partBotTwo">
              <div :class="{'innerBox':true, 'boderOne':true, 'newBox':true,'isPtActive':ptIndex == 0}"
                @click="changePt(0)">
                <div class="ltxt">行业级科创平台</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in String(ptArr[0].hyzs)" :key="i">
                    <div class="numtxtwo" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxtwo" v-else>{{item}}</div>
                  </div>
                  <span class="numtxtwo" style="font-size:32px;margin-top:64px;">个</span>
                </div>
              </div>

              <div :class="{'innerBox':true, 'boderOne':true, 'newBox':true,'isPtActive':ptIndex == 1}"
                @click="changePt(1)">
                <div class="ltxt">企业级科创平台</div>
                <div class="numberTwo">
                  <div v-for="(item, i) in String(ptArr[0].qyzs)" :key="i">
                    <div class="numtxt" v-if="item!=','&&item!='.'">
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                    </div>
                    <div class="numtxt" v-else>{{item}}</div>
                  </div>
                  <span class="numtxt" style="font-size:32px;margin-top:64px;">个</span>
                </div>
              </div>
            </div>
          </div>

          <div class="partTwo" id="chartThree"></div>
        </div>
      </div>

    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>

    var vm = new Vue({
      el: '#app',
      data: {
        qyIndex: 0,
        ptIndex: 0,
        zsArr: [{ zs: 0 }, { zs: 0 }, { zs: 0 }],
        ptArr: [{ hyzs: 0, qyzs: 0 }],
        gszcz: {
          value: '0',
          zs: '-'
        },
        cyzc: {
          zcsl: '-',
          dfqys: '-',
          zcdfje: '-'
        },
        rczy: {
          xxrc: '-',
          wsqyj: '-',
          gszldz: '-'
        },
        jsgg: {
          jbsl: '-',
          jbzje: '-',
          jbl: '-'
        }
      },

      created () {
        this.getData()
      },

      mounted () {
        // top.addEventListener('message', (e) => {
        //   console.log('子页面-右侧监听', e)
        // }, false)
        this.getChartsTwo()
        this.changeQy()
        this.changePt()
      },

      methods: {
        getData () {
          $api('csdn_qyhx57').then(res => {
            this.zsArr = res
          })

          $api('qyhx_cyl_kcpt').then(res => {
            this.ptArr[0].hyzs = res[1].sl
            this.ptArr[0].qyzs = res[0].sl
          })

          $api('csdn_qyhx68', { cyl_name: '100' }).then(res => {
            this.gszcz.value = res[0].gsgyzcz
            this.gszcz.zs = res[0].gsgyzczzs
          })

          $api('qyhx_cyl_xxrs').then(res => {
            this.rczy.xxrc = String(res[0].tjz)
            this.rczy.wsqyj = String(res[1].tjz)
            this.rczy.gszldz = String((res[2].tjz / 10000).toFixed(2))
          })

          $api('qyhx_hqzc_zczs').then(res => {
            this.cyzc.zcsl = res[0].tjz
          })

          $api('qyhx_cyl_zcdfsj').then(res => {
            // console.log('兑付企业数', res)
            this.cyzc.dfqys = res[0].dfqys
            this.cyzc.zcdfje = String((res[0].zcdfje / 100000000).toFixed(1))
          })

          $api('qlzf_cyzy_jbgs').then(res => {
            this.jsgg.jbsl = res[0].tjz
            this.jsgg.jbzje = String((Number(res[1].tjz) / 10000).toFixed(2))
            this.jsgg.jbl = res[2].tjz
          })
        },

        changePt (index = 0) {
          this.ptIndex = index
          let str = index == 0 ? '行业级' : '企业级'
          $api('qyhx_cyl_kcpt', { kcpt: str }).then(res => {
            let sort = res.map(el => el.cylmc)
            let xData = res.map(el => el.sl)
            this.getChartsThree(sort, xData)
          })
        },

        changeQy (index = 0) {
          this.qyIndex = index
          let sort = ["婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县"]
          switch (this.qyIndex) {
            case 0:
              $api('csdn_qyhx56', { qylx: "链主培育企业" }).then(res => {
                let xData = sort.map(el => {
                  return res.find(it => it.qx == el).zs
                })
                this.getChartsOne(sort, xData)
              })
              break
            case 1:
              $api('csdn_qyhx56', { qylx: "骨干企业" }).then(res => {
                let xData = sort.map(el => {
                  return res.find(it => it.qx == el).zs
                })
                this.getChartsOne(sort, xData)
              })
              break
            case 2:
              $api('csdn_qyhx56', { qylx: "星火企业" }).then(res => {
                let xData = sort.map(el => {
                  return res.find(it => it.qx == el).zs
                })
                this.getChartsOne(sort, xData)
              })
              break
          }
        },

        getChartsOne (name = [], datas = []) {
          let myChart = echarts.init(document.getElementById('chartOne'))
          let option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              backgroundColor: 'rgba(9, 24, 48, 0.5)',
              borderColor: 'rgba(75, 253, 238, 0.4)',
              textStyle: {
                color: '#CFE3FC',
                fontSize: '36',
              },
              borderWidth: 1,
            },
            grid: {
              top: '20%',
              right: '0%',
              left: "10%",
              bottom: '10%'
            },
            xAxis: [{
              type: 'category',
              data: name, // ["婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县"]
              axisLine: {
                lineStyle: {
                  color: 'rgba(122, 163, 197, 1)'
                }
              },
              axisLabel: {
                // margin: 10,
                interval: 0,
                color: '#fff',
                textStyle: {
                  fontSize: 24
                },
              },
              axisTick: {
                show: false
              }
            }],
            yAxis: [{
              name: "企业数量（家）",
              nameTextStyle: {
                color: '#fff',
                fontSize: 28,
                padding: [0, 0, 20, 0]
              },
              axisLabel: {
                formatter: '{value}',
                color: '#fff',
                textStyle: {
                  fontSize: 28
                },
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#FFFFFF'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(122, 163, 197, 0)'
                }
              }
            }],
            series: [{
              type: 'bar',
              data: datas,
              barWidth: '20px',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(19, 194, 194, 1)' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: 'rgba(0,77,167,0)' // 100% 处的颜色
                  }], false),
                  shadowColor: 'rgba(0,160,221,1)',
                  shadowBlur: 4,
                }
              },
              label: {
                normal: {
                  show: false
                }
              }
            }]
          }
          myChart.setOption(option, true)
        },

        getChartsTwo () {
          $api('csdn_qyhx68', { cyl_name: '' }).then(res => {
            // console.log('图表2数据', res)
            let dataArr = res.slice(0, 10)
            let valArr = dataArr.map(el => el.gsgyzcz)
            let zsArr = dataArr.map(el => el.gsgyzczzs)
            let myChart = echarts.init(document.getElementById('chartTwo'))
            let option = {
              tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                },
                backgroundColor: 'rgba(9, 24, 48, 0.5)',
                borderColor: 'rgba(75, 253, 238, 0.4)',
                textStyle: {
                  color: '#CFE3FC',
                  fontSize: '36',
                },
                borderWidth: 1,
                formatter: (data) => {
                  let html = ``
                  html = `<div>${data[0].name}</div>
                            <div>${data[0].marker} ${data[0].seriesName}: ${data[0].value}</div>
                            <div>${data[1].marker} ${data[1].seriesName}: ${data[1].value}%</div>
                        `
                  return html
                }
              },
              grid: {
                top: '20%',
                right: '0%',
                left: "12%",
                bottom: '20%'
              },
              xAxis: [{
                type: 'category',
                data: ["新能源汽车及关键零部件", "智能光伏及新型储能", "电动工具", "纺织服装", '生物医药及植入性医疗器械', '磁性材料', '集成电路及信创', '电子化学品',
                  '工业机床', '机器人'],
                axisLine: {
                  lineStyle: {
                    color: 'rgba(122, 163, 197, 1)'
                  }
                },
                axisLabel: {
                  margin: 20,
                  formatter: (value, index) => {
                    if (value.length > 3) {
                      return value.substring(0, 3) + '...'
                    } else {
                      return value
                    }
                  },
                  rotate: -30,
                  interval: 0, //设置为 1，表示『隔一个标签显示一个标签』
                  color: '#fff',
                  textStyle: {
                    fontSize: 28
                  },
                },
                axisTick: {
                  show: false
                }
              }],
              yAxis: [{
                name: "产值分布（亿元）",
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                  padding: [0, 0, 20, 0]
                },
                axisLabel: {
                  formatter: '{value}',
                  color: '#fff',
                  textStyle: {
                    fontSize: 28
                  },
                },
                axisTick: {
                  show: false
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#FFFFFF'
                  }
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: 'rgba(122, 163, 197, 0)'
                  }
                }
              }, {
                show: false,
                type: "value",
                name: "单位：%",
                axisLabel: {
                  fontSize: 14,
                  color: "#4e5969",
                  padding: [0, 0, 0, 5],
                },
                splitLine: {
                  show: false,
                },
              }],
              series: [{
                name: "产值分布",
                type: 'bar',
                // data: [222.98, 291.32, 131.14, 244.34, 55.72, 65.55, 20.23, 16.3, 20.28, 20.17],
                data: valArr,
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                      offset: 0,
                      color: 'rgba(0, 177, 255, 1)' // 0% 处的颜色
                    }, {
                      offset: 1,
                      color: 'rgba(0,77,167,0)' // 100% 处的颜色
                    }], false),
                    shadowColor: 'rgba(0,160,221,1)',
                    shadowBlur: 4,
                  }
                },
                label: {
                  normal: {
                    show: false
                  }
                }
              },
              {
                name: "产值增速",
                type: "line",
                symbolSize: "0",
                smooth: false,
                itemStyle: {
                  color: "#FF7D00",
                },
                lineStyle: {
                  color: "#FF7D00",
                },
                yAxisIndex: 1,
                // data: [30, -27.5, 17.5, 10, -5.2, -7.4, 45.9, 3.5, 1.8, 6.9]
                data: zsArr
              }]
            }
            myChart.setOption(option, true)
          })
        },

        getChartsThree (name = [], datas = []) {
          let myChart = echarts.init(document.getElementById('chartThree'))
          let option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              backgroundColor: 'rgba(9, 24, 48, 0.5)',
              borderColor: 'rgba(75, 253, 238, 0.4)',
              textStyle: {
                color: '#CFE3FC',
                fontSize: '36',
              },
              borderWidth: 1,
            },
            grid: {
              top: '20%',
              right: '0%',
              left: "10%",
              bottom: '25%'
            },
            xAxis: [{
              type: 'category',
              data: name, // ["婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县"]
              axisLine: {
                lineStyle: {
                  color: 'rgba(122, 163, 197, 1)'
                }
              },
              axisLabel: {
                // margin: 10,
                interval: 0,
                color: '#fff',
                textStyle: {
                  fontSize: 24
                },
                rotate: -30,
                formatter: (value, index) => {
                  if (value.length > 3) {
                    return value.substring(0, 3) + '...'
                  } else {
                    return value
                  }
                },
              },
              axisTick: {
                show: false
              }
            }],
            yAxis: [{
              name: "单位（个）",
              nameTextStyle: {
                color: '#fff',
                fontSize: 28,
                padding: [0, 0, 20, 0]
              },
              axisLabel: {
                formatter: '{value}',
                color: '#fff',
                textStyle: {
                  fontSize: 28
                },
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#FFFFFF'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(122, 163, 197, 0)'
                }
              }
            }],
            series: [{
              type: 'bar',
              data: datas,
              barWidth: '20px',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(231, 121, 48, 1)' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: 'rgba(231, 121, 48, 0)' // 100% 处的颜色
                  }], false),
                  shadowColor: 'rgba(231, 121, 48, 1)',
                  shadowBlur: 4,
                }
              },
              label: {
                normal: {
                  show: false
                }
              }
            }]
          }
          myChart.setOption(option, true)
        },
        

      },
    })
  </script>
</body>